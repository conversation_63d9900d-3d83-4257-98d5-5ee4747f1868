package com.BE.mapper;

import com.BE.model.entity.SlideDetail;
import com.BE.model.response.SlideDetailResponse;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface SlideDetailMapper {

    // Convert entity → response
    @Mapping(source = "slideTemplate.id", target = "slideTemplateId")
    @Mapping(source = "slideTemplate.name", target = "slideTemplateName")
    SlideDetailResponse toResponse(SlideDetail entity);
}
