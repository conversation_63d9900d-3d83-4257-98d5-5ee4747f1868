package com.BE.model.response;

import com.BE.enums.StatusEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SlideDetailResponse {
    String id;
    String title;
    String slideData; // JSON string của slide
    String description; // Mô tả placeholder
    StatusEnum status;
    Long slideTemplateId;
    String slideTemplateName;
    String createdAt;
    String updatedAt;
}
