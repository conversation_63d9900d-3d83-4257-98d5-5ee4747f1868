# API Testing Guide

## 1. Test SlidePlaceholder APIs

### Get All Placeholders
```bash
curl -X GET "http://localhost:8091/api/slide-placeholders" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1"
```

### Get Placeholder by ID
```bash
curl -X GET "http://localhost:8091/api/slide-placeholders/1" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1"
```

### Create New Placeholder
```bash
curl -X POST "http://localhost:8091/api/slide-placeholders" \
  -H "Content-Type: application/json" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1" \
  -d '{
    "type": "LessonName",
    "name": "Tên bài học mới",
    "description": "<PERSON>ô tả cho placeholder tên bài học"
  }'
```

## 2. Test SlideTemplate with SlideDetail Integration

### Create SlideTemplate with JSON Data
```bash
curl -X POST "http://localhost:8091/api/slide-templates" \
  -H "Content-Type: application/json" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1" \
  -d '{
    "name": "Template Test",
    "description": "Template for testing",
    "textBlocks": {
      "title": "Test Title"
    },
    "imageBlocks": {
      "image1": "test.jpg"
    },
    "slideDataJson": "{\"version\":\"1.0\",\"createdAt\":\"2025-07-21T08:27:51.937Z\",\"slideFormat\":\"16:9\",\"slides\":[{\"id\":\"p1\",\"title\":\"Slide 1\",\"elements\":[{\"id\":\"p1_i1559\",\"type\":\"text\",\"x\":136,\"y\":96,\"width\":701,\"height\":191,\"zIndex\":0,\"text\":\"LessonName 70\",\"style\":{\"bold\":true,\"color\":\"#ffffff\",\"italic\":false,\"fontSize\":40,\"textAlign\":\"center\",\"underline\":false,\"fontFamily\":\"Arial\",\"backgroundColor\":\"transparent\"}},{\"id\":\"p1_i1560\",\"type\":\"text\",\"x\":185,\"y\":271,\"width\":595,\"height\":136,\"zIndex\":1,\"text\":\"LessonDescription 200\",\"style\":{\"bold\":false,\"color\":\"#ffffff\",\"italic\":false,\"fontSize\":14,\"textAlign\":\"center\",\"underline\":false,\"fontFamily\":\"Arial\",\"backgroundColor\":\"rgb(94, 108, 213)\"}},{\"id\":\"g36ebc629d7b_0_131\",\"type\":\"text\",\"x\":248,\"y\":434,\"width\":475,\"height\":43,\"zIndex\":2,\"text\":\"CreatedDate 50\",\"style\":{\"bold\":true,\"color\":\"#ffffff\",\"italic\":false,\"fontSize\":12,\"textAlign\":\"center\",\"underline\":false,\"fontFamily\":\"Arial\",\"backgroundColor\":\"rgb(94, 108, 213)\"}}],\"isVisible\":true,\"background\":\"\"}]}"
  }'
```

## 3. Test SlideDetail APIs

### Get SlideDetails by Template ID
```bash
curl -X GET "http://localhost:8091/api/slide-details/template/1" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1"
```

### Get SlideDetail by ID
```bash
curl -X GET "http://localhost:8091/api/slide-details/p1" \
  -H "X-Username: testuser" \
  -H "X-User-Role: USER" \
  -H "X-User-Id: 1"
```

## Expected Results

1. **SlidePlaceholder**: Should return 9 default placeholders (LessonName, LessonDescription, etc.)
2. **SlideTemplate**: Should create template and automatically create SlideDetail entries
3. **SlideDetail**: Should show placeholder analysis like "1 LessonName, 1 LessonDescription, 1 CreatedDate"

## Database Tables Created

1. **slide_placeholder**: Stores placeholder definitions
2. **slide_detail**: Stores individual slides with JSON data and placeholder analysis
3. **slide_template**: Updated with relationship to slide_detail

## Swagger Documentation

Access API documentation at: http://localhost:8091/swagger-ui/index.html
